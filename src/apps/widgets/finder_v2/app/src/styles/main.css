@import "tailwindcss";
@import "./brand-icons.css";

/* TailwindCSS v4 Theme Configuration */
@theme {
  /* Default theme colors - can be overridden by CSS custom properties */
  --color-ws-primary-50: #f0f9ff;
  --color-ws-primary-100: #e0f2fe;
  --color-ws-primary-200: #bae6fd;
  --color-ws-primary-300: #7dd3fc;
  --color-ws-primary-400: #38bdf8;
  --color-ws-primary-500: var(--theme-primary, #0ea5e9);
  --color-ws-primary-600: var(--theme-primary, #0284c7);
  --color-ws-primary-700: var(--theme-primary, #0369a1);
  --color-ws-primary-800: #075985;
  --color-ws-primary-900: #0c4a6e;

  --color-ws-secondary-50: #f8fafc;
  --color-ws-secondary-100: #f1f5f9;
  --color-ws-secondary-200: #e2e8f0;
  --color-ws-secondary-300: var(--theme-secondary, #cbd5e1);
  --color-ws-secondary-400: var(--theme-secondary, #94a3b8);
  --color-ws-secondary-500: var(--theme-secondary, #64748b);
  --color-ws-secondary-600: var(--theme-secondary, #475569);
  --color-ws-secondary-700: #334155;
  --color-ws-secondary-800: #1e293b;
  --color-ws-secondary-900: #0f172a;

  --color-indigo-200: #c7d2fe;
  --color-indigo-500: #6366f1;
  --color-indigo-600: #4f46e5;

  /* Theme-aware accent colors */
  --color-ws-accent-500: var(--theme-accent, #10b981);
  --color-ws-accent-600: var(--theme-accent, #059669);

  /* Theme-aware typography */
  --font-sans: var(--theme-font-family, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif);
  --font-family-sans: var(--theme-font-family, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif);
  --font-size-base: var(--theme-font-size, 16px);
  --font-weight-base: var(--theme-font-weight, 400);
  --line-height-base: var(--theme-line-height, 1.5);
  --letter-spacing-base: var(--theme-letter-spacing, 0);
  
  /* Theme-aware font sizes that scale with base font size */
  --text-xs: calc(var(--font-size-base) * 0.75);
  --text-sm: calc(var(--font-size-base) * 0.875);
  --text-base: var(--font-size-base);
  --text-lg: calc(var(--font-size-base) * 1.125);
  --text-xl: calc(var(--font-size-base) * 1.25);
  --text-2xl: calc(var(--font-size-base) * 1.5);
  --text-3xl: calc(var(--font-size-base) * 1.875);
  
  /* Theme-aware spacing */
  --padding-base: var(--theme-padding, 0.75rem);
  --margin-base: var(--theme-margin, 0.5rem);
  
  /* Theme-aware layout */
  --border-radius-base: var(--theme-border-radius, 0.375rem);
  --border-width-base: var(--theme-border-width, 1px);
  --shadow-base: var(--theme-shadow, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06));
  
  /* Theme-aware animations */
  --animation-speed-base: var(--theme-animation-speed, 0.3s);
  --hover-effect-base: var(--theme-hover-effect, brightness(0.9));
  
  /* Widget layout */
  --max-width-widget: 100%;
  --min-width-widget: 250px;
}

/* Theme-aware utility classes for primary colors */
.bg-primary-color {
  background-color: var(--theme-primary, #3b82f6) !important;
}

.text-primary-color {
  color: var(--theme-primary, #3b82f6) !important;
}

.border-primary-color {
  border-color: var(--theme-primary, #3b82f6) !important;
}

.focus\:outline-primary-color:focus {
  outline-color: var(--theme-primary, #3b82f6) !important;
}

.focus\:border-primary-color:focus {
  border-color: var(--theme-primary, #3b82f6) !important;
}

.hover\:bg-primary-color:hover {
  background-color: var(--theme-primary, #3b82f6) !important;
}

/* Finder-v2 Widget Styles */

/* Base widget container */
.widget,
.finder-v2-widget {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-base);
  line-height: var(--line-height-base);
  letter-spacing: var(--letter-spacing-base);
  color: var(--theme-text, #374151);
  background-color: var(--theme-background, transparent);
  max-width: var(--max-width-widget);
  min-width: var(--min-width-widget);
}

/* Widget tabs styling removed - finder-v2 only supports single search type */

/* External spinner animation for CustomSelector loading states */
.spinner-external svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Form styling */
.form-group {
  margin-bottom: 1rem;
}

/* Remove bottom margin for form-group in grid layout */
.form-grid .form-group {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.form-control {
  width: 100%;
  padding: var(--padding-base, 0.5rem 0.75rem);
  margin: var(--margin-base, 0.5rem) 0;
  border: var(--border-width-base) solid var(--theme-secondary, #d1d5db);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-base);
  line-height: var(--line-height-base);
  letter-spacing: var(--letter-spacing-base);
  color: var(--theme-text, #374151);
  background-color: var(--theme-background, #ffffff);
  transition: all var(--animation-speed-base) ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--color-ws-accent-500);
  box-shadow: 0 0 0 3px rgba(var(--theme-accent-rgb, 16, 185, 129), 0.1);
}

.form-control:disabled {
  background-color: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
}

/* Button styling */
.btn {
  display: inline-flex;
  align-items: center;
  padding: var(--padding-base, 0.5rem 1rem);
  margin: var(--margin-base, 0.5rem) 0;
  border: var(--border-width-base) solid transparent;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-base, 500);
  line-height: var(--line-height-base);
  letter-spacing: var(--letter-spacing-base);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-base);
  cursor: pointer;
  transition: all var(--animation-speed-base) ease;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  color: white;
  background-color: var(--color-ws-primary-600);
}

.btn-primary:hover {
  background-color: var(--color-ws-primary-700);
  filter: var(--hover-effect-base);
  transform: translateY(-1px);
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb, 14, 165, 233), 0.3);
}

.btn-secondary {
  color: #374151;
  background-color: white;
  border-color: #d1d5db;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

/* Loading states */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
}

.spinner {
  animation: spin 1s linear infinite;
  height: 1.25rem;
  width: 1.25rem;
  color: var(--color-ws-primary-600);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Results table */
.results-table {
  width: 100%;
  border-collapse: collapse;
}

.results-table th {
  padding: 0.5rem 1rem;
  text-align: left;
  font-size: var(--text-xs);
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.results-table td {
  padding: 0.75rem 1rem;
  font-size: var(--text-sm);
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
}

.results-table tr.oem {
  background-color: #fefce8;
}

.results-table tr.oem td {
  color: #92400e;
}

/* Responsive design */
@media (max-width: 640px) {
  .widget {
    font-size: 13px;
  }

  .form-control {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .results-table th,
  .results-table td {
    padding: 0.5rem;
    font-size: var(--text-xs);
  }
}

/* Widget-specific animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--animation-speed-base) ease;
}

/* Theme-specific hover effects */
.hover-scale:hover {
  transform: scale(1.05);
  transition: transform var(--animation-speed-base) ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.6);
  transition: box-shadow var(--animation-speed-base) ease;
}

.hover-shadow:hover {
  box-shadow: 0 8px 16px rgba(var(--theme-primary-rgb, 59, 130, 246), 0.3);
  transition: box-shadow var(--animation-speed-base) ease;
}

/* Advanced theme responsive overrides */
@media (max-width: 767px) {
  .finder-v2-widget {
    --font-size-base: var(--theme-mobile-font-size, var(--theme-font-size, 16px));
    --padding-base: var(--theme-mobile-padding, var(--theme-padding, 0.5rem));
    --margin-base: var(--theme-mobile-margin, var(--theme-margin, 0.25rem));
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .finder-v2-widget {
    --font-size-base: var(--theme-tablet-font-size, var(--theme-font-size, 16px));
    --padding-base: var(--theme-tablet-padding, var(--theme-padding, 0.625rem));
    --margin-base: var(--theme-tablet-margin, var(--theme-margin, 0.375rem));
  }
}

@media (min-width: 1024px) {
  .finder-v2-widget {
    --font-size-base: var(--theme-desktop-font-size, var(--theme-font-size, 16px));
    --padding-base: var(--theme-desktop-padding, var(--theme-padding, 0.75rem));
    --margin-base: var(--theme-desktop-margin, var(--theme-margin, 0.5rem));
  }
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Error states */
.error {
  color: #dc2626;
  font-size: var(--text-sm);
  margin-top: 0.25rem;
}

.error-border {
  border-color: #fca5a5;
}

.error-border:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Theme-aware border radius classes */
.theme-rounded {
  border-radius: var(--border-radius-base);
}

.theme-rounded-md {
  border-radius: var(--border-radius-base);
}
