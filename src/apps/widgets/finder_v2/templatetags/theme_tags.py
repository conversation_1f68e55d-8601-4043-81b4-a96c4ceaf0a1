"""
Template tags for theme CSS rendering in finder-v2 widgets.

This module provides template tags for injecting theme CSS into widget templates,
enabling dynamic theme application in the iframe context.
"""

import logging
from django import template
from django.utils.safestring import mark_safe

logger = logging.getLogger(__name__)
register = template.Library()


def _get_hover_effect_value(hover_effect):
    """Convert hover effect name to CSS value."""
    effects = {
        'darken': 'brightness(0.9)',
        'lighten': 'brightness(1.1)',
        'brightness': 'brightness(0.95)',
        'opacity': 'opacity(0.8)',
        'scale': 'scale(1.05)',
        'shadow': 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2))',
    }
    return effects.get(hover_effect, 'brightness(0.9)')


def _get_font_family_value(font_family):
    """Convert font family name to CSS font stack with proper fallbacks."""
    font_stacks = {
        'system-ui': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        'Inter': '"Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        'Roboto': '"Roboto", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        'Open Sans': '"Open Sans", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        'Lato': '"Lato", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        'Montserrat': '"Montserrat", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        'Poppins': '"Poppins", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        'custom': font_family  # For custom fonts, use as-is
    }
    return font_stacks.get(font_family, f'"{font_family}", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif')


def _get_shadow_value(shadow_intensity):
    """Convert shadow intensity to CSS value."""
    shadows = {
        'none': 'none',
        'light': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'medium': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'heavy': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    }
    return shadows.get(shadow_intensity, shadows['medium'])


@register.simple_tag
def render_theme_css(theme):
    """
    Render theme CSS as an HTML style tag.
    
    Args:
        theme: WidgetTheme instance
        
    Returns:
        HTML style tag with theme CSS
    """
    if not theme:
        logger.warning("🎨 RENDER_THEME_CSS: Received no theme object. CSS will not be rendered.")
        return ""
    
    logger.info(f"🎨 RENDER_THEME_CSS: Rendering CSS for theme '{getattr(theme, 'theme_name', 'N/A')}' (ID: {getattr(theme, 'id', 'N/A')})")
    logger.info(f"🎨 RENDER_THEME_CSS:   - Widget: {getattr(theme, 'widget', 'N/A')}")
    logger.info(f"🎨 RENDER_THEME_CSS:   - Primary Color: {getattr(theme, 'primary_color', 'N/A')}")
    logger.info(f"🎨 RENDER_THEME_CSS:   - Background Color: {getattr(theme, 'background_color', 'N/A')}")
    
    # Enhanced CSS with all theme properties
    css = f"""
    <style type="text/css">
    :root {{
        --theme-primary: {getattr(theme, 'primary_color', '#3B82F6')};
        --theme-secondary: {getattr(theme, 'secondary_color', '#6B7280')};
        --theme-accent: {getattr(theme, 'accent_color', '#10B981')};
        --theme-background: {getattr(theme, 'background_color', '#FFFFFF')};
        --theme-text: {getattr(theme, 'text_color', '#1F2937')};
        --theme-font-family: {_get_font_family_value(getattr(theme, 'font_family', 'system-ui'))};
        --theme-font-size: {getattr(theme, 'base_font_size', '16px')};
        --theme-font-weight: {getattr(theme, 'font_weight', '400')};
        --theme-line-height: {getattr(theme, 'line_height', '1.5')};
        --theme-letter-spacing: {getattr(theme, 'letter_spacing', '0')};
        --theme-padding: {getattr(theme, 'element_padding', '0.75rem')};
        --theme-margin: {getattr(theme, 'element_margin', '0.5rem')};
        --theme-border-radius: {getattr(theme, 'border_radius', '0.375rem')};
        --theme-border-width: {getattr(theme, 'border_width', '1px')};
        --theme-animation-speed: {getattr(theme, 'animation_speed', '0.3s')};
        --theme-hover-effect: {_get_hover_effect_value(getattr(theme, 'hover_effect', 'darken'))};
        --theme-shadow: {_get_shadow_value(getattr(theme, 'shadow_intensity', 'medium'))};
        
        /* Derived color variables for better theming */
        --theme-primary-rgb: {theme._hex_to_rgb(getattr(theme, 'primary_color', '#3B82F6')) if hasattr(theme, '_hex_to_rgb') else '59, 130, 246'};
        --theme-secondary-rgb: {theme._hex_to_rgb(getattr(theme, 'secondary_color', '#6B7280')) if hasattr(theme, '_hex_to_rgb') else '107, 114, 128'};
        --theme-accent-rgb: {theme._hex_to_rgb(getattr(theme, 'accent_color', '#10B981')) if hasattr(theme, '_hex_to_rgb') else '16, 185, 129'};
    }}
    
    /* Enhanced component styles using all theme custom properties */
    .finder-v2-widget {{
        background-color: var(--theme-background);
        color: var(--theme-text);
        font-family: var(--theme-font-family);
        font-size: var(--theme-font-size);
        font-weight: var(--theme-font-weight);
        line-height: var(--theme-line-height);
        letter-spacing: var(--theme-letter-spacing);
    }}
    
    .finder-v2-widget .btn-primary {{
        background-color: var(--theme-primary);
        border-color: var(--theme-primary);
        border-width: var(--theme-border-width);
        border-radius: var(--theme-border-radius);
        padding: var(--theme-padding);
        margin: var(--theme-margin);
        box-shadow: var(--theme-shadow);
        transition: all var(--theme-animation-speed) ease;
        font-weight: var(--theme-font-weight);
    }}
    
    .finder-v2-widget .btn-primary:hover {{
        background-color: var(--theme-primary);
        filter: var(--theme-hover-effect);
        transform: translateY(-1px);
    }}
    
    .finder-v2-widget .form-control {{
        border-color: var(--theme-secondary);
        border-width: var(--theme-border-width);
        border-radius: var(--theme-border-radius);
        padding: var(--theme-padding);
        margin: var(--theme-margin);
        font-size: var(--theme-font-size);
        font-weight: var(--theme-font-weight);
        line-height: var(--theme-line-height);
        letter-spacing: var(--theme-letter-spacing);
        transition: all var(--theme-animation-speed) ease;
    }}
    
    .finder-v2-widget .form-control:focus {{
        border-color: var(--theme-accent);
        box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        transform: scale(1.02);
    }}
    
    .finder-v2-widget .alert-success {{
        background-color: var(--theme-accent);
        border-color: var(--theme-accent);
        border-width: var(--theme-border-width);
        border-radius: var(--theme-border-radius);
        padding: var(--theme-padding);
        margin: var(--theme-margin);
    }}
    
    .finder-v2-widget .card {{
        background-color: var(--theme-background);
        border-color: var(--theme-secondary);
        border-width: var(--theme-border-width);
        border-radius: var(--theme-border-radius);
        box-shadow: var(--theme-shadow);
        padding: var(--theme-padding);
        margin: var(--theme-margin);
    }}
    
    /* Utility classes for easy theme usage in custom templates */
    
    /* Text color utilities */
    .text-primary-color {{
        color: var(--theme-primary) !important;
    }}
    
    .text-secondary-color {{
        color: var(--theme-secondary) !important;
    }}
    
    .text-accent-color {{
        color: var(--theme-accent) !important;
    }}
    
    .text-background-color {{
        color: var(--theme-background) !important;
    }}
    
    .text-main-color {{
        color: var(--theme-text) !important;
    }}
    
    /* Background color utilities */
    .bg-primary-color {{
        background-color: var(--theme-primary) !important;
    }}
    
    .bg-secondary-color {{
        background-color: var(--theme-secondary) !important;
    }}
    
    .bg-accent-color {{
        background-color: var(--theme-accent) !important;
    }}
    
    .bg-background-color {{
        background-color: var(--theme-background) !important;
    }}
    
    .bg-main-color {{
        background-color: var(--theme-text) !important;
    }}
    
    /* Border color utilities */
    .border-primary-color {{
        border-color: var(--theme-primary) !important;
    }}
    
    .border-secondary-color {{
        border-color: var(--theme-secondary) !important;
    }}
    
    .border-accent-color {{
        border-color: var(--theme-accent) !important;
    }}
    
    .border-background-color {{
        border-color: var(--theme-background) !important;
    }}
    
    .border-main-color {{
        border-color: var(--theme-text) !important;
    }}
    
    /* Hover utilities for theme colors */
    .hover\\:text-primary-color:hover {{
        color: var(--theme-primary) !important;
    }}
    
    .hover\\:text-secondary-color:hover {{
        color: var(--theme-secondary) !important;
    }}
    
    .hover\\:text-accent-color:hover {{
        color: var(--theme-accent) !important;
    }}
    
    .hover\\:bg-primary-color:hover {{
        background-color: var(--theme-primary) !important;
    }}
    
    .hover\\:bg-secondary-color:hover {{
        background-color: var(--theme-secondary) !important;
    }}
    
    .hover\\:bg-accent-color:hover {{
        background-color: var(--theme-accent) !important;
    }}
    
    /* Additional theme utilities */
    .theme-font-family {{
        font-family: var(--theme-font-family) !important;
    }}
    
    .theme-font-size {{
        font-size: var(--theme-font-size) !important;
    }}
    
    .theme-border-radius {{
        border-radius: var(--theme-border-radius) !important;
    }}
    
    .theme-padding {{
        padding: var(--theme-padding) !important;
    }}
    
    .theme-margin {{
        margin: var(--theme-margin) !important;
    }}
    
    .theme-shadow {{
        box-shadow: var(--theme-shadow) !important;
    }}
    
    /* Opacity variants for theme colors */
    .bg-primary-color\\/5 {{
        background-color: rgba(var(--theme-primary-rgb), 0.05) !important;
    }}
    
    .bg-primary-color\\/10 {{
        background-color: rgba(var(--theme-primary-rgb), 0.1) !important;
    }}
    
    .bg-primary-color\\/20 {{
        background-color: rgba(var(--theme-primary-rgb), 0.2) !important;
    }}
    
    .bg-primary-color\\/50 {{
        background-color: rgba(var(--theme-primary-rgb), 0.5) !important;
    }}
    
    .bg-secondary-color\\/5 {{
        background-color: rgba(var(--theme-secondary-rgb), 0.05) !important;
    }}
    
    .bg-secondary-color\\/10 {{
        background-color: rgba(var(--theme-secondary-rgb), 0.1) !important;
    }}
    
    .bg-secondary-color\\/20 {{
        background-color: rgba(var(--theme-secondary-rgb), 0.2) !important;
    }}
    
    .bg-secondary-color\\/50 {{
        background-color: rgba(var(--theme-secondary-rgb), 0.5) !important;
    }}
    
    .bg-accent-color\\/5 {{
        background-color: rgba(var(--theme-accent-rgb), 0.05) !important;
    }}
    
    .bg-accent-color\\/10 {{
        background-color: rgba(var(--theme-accent-rgb), 0.1) !important;
    }}
    
    .bg-accent-color\\/20 {{
        background-color: rgba(var(--theme-accent-rgb), 0.2) !important;
    }}
    
    .bg-accent-color\\/50 {{
        background-color: rgba(var(--theme-accent-rgb), 0.5) !important;
    }}
    </style>
    """
    
    return mark_safe(css)


@register.filter
def has_theme(widget_config):
    """
    Check if a widget has a theme configured.
    
    Args:
        widget_config: WidgetConfig instance
        
    Returns:
        Boolean indicating if theme is configured
    """
    return hasattr(widget_config, 'theme') and widget_config.theme is not None


@register.filter
def theme_color_rgb(hex_color):
    """
    Convert hex color to RGB values.
    
    Args:
        hex_color: Hex color string (e.g., '#FF0000')
        
    Returns:
        RGB values as comma-separated string (e.g., '255, 0, 0')
    """
    if not hex_color or not hex_color.startswith('#'):
        return "0, 0, 0"
    
    hex_color = hex_color.lstrip('#')
    
    # Handle 3-digit hex colors
    if len(hex_color) == 3:
        hex_color = ''.join([c*2 for c in hex_color])
    
    if len(hex_color) != 6:
        return "0, 0, 0"
    
    try:
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        return f"{r}, {g}, {b}"
    except ValueError:
        return "0, 0, 0"